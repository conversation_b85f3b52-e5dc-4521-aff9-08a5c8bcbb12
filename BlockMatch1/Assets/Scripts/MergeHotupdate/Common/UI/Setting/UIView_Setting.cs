using DragonPlus;
using DragonPlus.Account;
using DragonPlus.Core;
using DragonPlus.InAppPurchasing;
using DragonPlus.Native.Bridge;
using DragonPlus.Save;
using Framework;
using MainLauncher.Utils;
using TMGame;
using TMGame.Storage;
using UnityEngine;
using UnityEngine.UI;
using YooAsset;

/// <summary>
/// 设置界面
/// </summary>
public class UIView_Setting : UIView_SettingBase
{
    protected override void RegisterUIEvent()
    {
        base.RegisterUIEvent();
        UIBtn_CloseButton.onClick.AddListener(OnCloseBtn);
        UIBtn_Music.onClick.AddListener(OnMusicBtn);
        UIBtn_Sound.onClick.AddListener(OnSoundBtn);
        UIBtn_Vibrate.onClick.AddListener(OnVibrateBtn);
        UIBtn_Notice.onClick.AddListener(OnNoticeBtn);
        UIBtn_LanguageButton.onClick.AddListener(OnLanguageBtn);
        UIBtn_SupportButton.onClick.AddListener(OnContactUsBtn);
        UIBtn_OtherButton.onClick.AddListener(HandleShowOther);

        UIBtn_RestoreBuyButton.onClick.AddListener(OnRestoreBtn);
        UIBtn_SaveButton.onClick.AddListener(OnSaveProgressBtn);
        UIBtn_Privacy.onClick.AddListener(OnPrivacyBtn);
        UIBtn_Service.onClick.AddListener(OnServerBtn);
        UIBtn_Deletion.onClick.AddListener(OnDeleteAccountBtn);

        UIBtn_CloseButton.gameObject.SetActive(true);
        UIBtn_Music.gameObject.SetActive(true);
        UIBtn_Sound.gameObject.SetActive(true);
        UIBtn_Vibrate.gameObject.SetActive(true);
        UIBtn_LanguageButton.gameObject.SetActive(true);
        UIBtn_SupportButton.gameObject.SetActive(true);
        UIBtn_OtherButton.gameObject.SetActive(true);
        UIBtn_Privacy.gameObject.SetActive(true);
        UIBtn_Service.gameObject.SetActive(true);

        UIBtn_RestoreBuyButton.gameObject.SetActive(false);
        UIBtn_SaveButton.gameObject.SetActive(false);
        UIBtn_Deletion.gameObject.SetActive(false);
        UIBtn_Notice.gameObject.SetActive(false);
    }

    protected override void RegisterGameEvent()
    {
        base.RegisterGameEvent();
        EventBus.Subscribe<EventOnApplicationPause>(OnApplicationPause);
        EventBus.Subscribe<EventLanguageChange>(OnEventLanguageChange);
    }

    protected override void OnOpen()
    {
        base.OnOpen();
        RefreshView();
    }

    protected override void OnClose()
    {
        base.OnClose();
        EventBus.Unsubscribe<EventOnApplicationPause>(OnApplicationPause);
        EventBus.Unsubscribe<EventLanguageChange>(OnEventLanguageChange);
    }

    private void RefreshView()
    {
        string playerIdStr = SDKUtil.Misc.PlayerIdToString(SDK<IStorage>.Instance.Get<StorageCommon>().PlayerId);
        UITxt_UserText.SetText(CoreUtils.GetLocalization("UI_setting_user_id") + playerIdStr);
        UITxt_VersionText.SetText($"Version {GameConfig.Version}");
        RefreshView_MusicBtn();
        RefreshView_SoundBtn();
        RefreshView_VibrateBtn();
        RefreshView_NoticeBtn();
    }

    private void RefreshView_MusicBtn()
    {
        var isOff = GameGlobal.GetMod<ConfigSys>().MusicClose;
        UIImg_MusicOff.gameObject.SetActive(isOff);
        UIImg_MusicOn.gameObject.SetActive(!isOff);
    }

    private void RefreshView_SoundBtn()
    {
        var isOff = GameGlobal.GetMod<ConfigSys>().SoundClose;
        UIImg_SoundOff.gameObject.SetActive(isOff);
        UIImg_SoundOn.gameObject.SetActive(!isOff);
    }

    private void RefreshView_VibrateBtn()
    {
        var isOff = GameGlobal.GetMod<ConfigSys>().VibrateClose;
        UIImg_VibrateOff.gameObject.SetActive(isOff);
        UIImg_VibrateOn.gameObject.SetActive(!isOff);
    }

    private void RefreshView_NoticeBtn()
    {
        var isOn = TMGame.GameGlobal.GetMod<NotificationSys>().IsNotificationOn();
        UIImg_NoticeOn.gameObject.SetActive(isOn);
        UIImg_NoticeOff.gameObject.SetActive(!isOn);
    }

    private void OnLanguageBtn()
    {
        GameGlobal.GetMgr<UIMgr>().OpenSync(UIViewName.UIView_SetLanguage);
    }

    private void OnCloseBtn()
    {
        if(isOtherShow)
        {
            HandleHideOther();
            GameGlobal.GetMgr<SoundMgr>().PlaySfx("block_common_1");
            return;
        }

        Close();
    }

    private void UpdateLayout()
    {
        LayoutRebuilder.ForceRebuildLayoutImmediate(UILayoutV_InsideGroup.GetComponent<RectTransform>());
        LayoutRebuilder.ForceRebuildLayoutImmediate(UILayoutV_ReplaceDiff.GetComponent<RectTransform>());
    }

    bool isOtherShow = false;
    void HandleShowOther()
    {
        if (isOtherShow) return;

        isOtherShow = true;
        UIBtn_SaveButton.gameObject.SetActive(true);
        UIBtn_Deletion.gameObject.SetActive(true);

        UIBtn_LanguageButton.gameObject.SetActive(false);
        UIBtn_SupportButton.gameObject.SetActive(false);
        UIBtn_OtherButton.gameObject.SetActive(false);

        UpdateLayout();
    }

    void HandleHideOther()
    {
        if (isOtherShow == false) return;

        isOtherShow = false;
        UIBtn_SaveButton.gameObject.SetActive(false);
        UIBtn_Deletion.gameObject.SetActive(false);

        UIBtn_LanguageButton.gameObject.SetActive(true);
        UIBtn_SupportButton.gameObject.SetActive(true);
        UIBtn_OtherButton.gameObject.SetActive(true);

        UpdateLayout();
    }

    private void OnMusicBtn()
    {
        GameGlobal.GetMod<ConfigSys>().MusicClose = !GameGlobal.GetMod<ConfigSys>().MusicClose;
        RefreshView_MusicBtn();
    }

    private void OnDeleteAccountBtn()
    {
        if (SDK<IStorage>.Instance.Get<StorageGlobal>().IsSubmitDeleteAccount)
        {
            ShowSubmitDeleteAccountView();
        }
        else
        {
            //UIView_Notice.ViewData viewData = new UIView_Notice.ViewData()
            //{
            //    titleKey = LocalizationManager.Instance.GetLocalizedString("&key.UI_login_button_delete_account"),
            //    content = LocalizationManager.Instance.GetLocalizedString("&key.UI_delete_txt1"),
            //    showCloseBtn = false,
            //    onRightBtn = () =>
            //    {
            //        SDK<IStorage>.Instance.Get<StorageGlobal>().IsSubmitDeleteAccount = true;
            //        ShowSubmitDeleteAccountView();
            //    },
            //};
            //Close();
            GameGlobal.GetMgr<UIMgr>().OpenSync(UIViewName.UIView_Delete);
        }
    }

    private void ShowSubmitDeleteAccountView()
    {
        UIView_Notice.ViewData viewData = new UIView_Notice.ViewData()
        {
            titleKey = LocalizationManager.Instance.GetLocalizedString("&key.UI_login_button_delete_account"),
            content = LocalizationManager.Instance.GetLocalizedString("&key.UI_login_tip_delete_account_7days"),
            showMidBtn = true,
            showCloseBtn = false,
        };
        GameGlobal.GetMgr<UIMgr>().OpenSync(UIViewName.UIView_Notice, viewData);
    }

    private void OnSoundBtn()
    {
        GameGlobal.GetMod<ConfigSys>().SoundClose = !GameGlobal.GetMod<ConfigSys>().SoundClose;
        RefreshView_SoundBtn();
    }

    private void OnVibrateBtn()
    {
        GameGlobal.GetMod<ConfigSys>().VibrateClose = !GameGlobal.GetMod<ConfigSys>().VibrateClose;
        RefreshView_VibrateBtn();
    }

    private void OnNoticeBtn()
    {
        SDK<INative>.Instance.OpenNotificationSetting();
    }

    private void OnContactUsBtn()
    {
        GameGlobal.GetMgr<UIMgr>().OpenSync(UIViewName.UIView_ContactUs);
    }

    private void OnSaveProgressBtn()
    {
        UIView_SaveYourProgress.ViewData viewData = new UIView_SaveYourProgress.ViewData()
        {
            isFromGame = true,
        };
        GameGlobal.GetMgr<UIMgr>().OpenSync(UIViewName.UIView_SaveYourProgress, viewData);
    }

    private void OnPrivacyBtn()
    {
        CommonUtils.OpenURL(ConfigurationController.Instance.PrivacyPolicyURL);
    }

    private void OnServerBtn()
    {
        CommonUtils.OpenURL(ConfigurationController.Instance.TermsOfServiceURL);
    }

    private void OnRestoreBtn()
    {
        if (!SDK<IAP>.Instance.IsInitialized())
        {
            UIView_Notice.ViewData viewData1 = new UIView_Notice.ViewData()
            {
                showMidBtn = true,
#if UNITY_ANDROID
                content = LocalizationManager.Instance.GetLocalizedString("&key.UI_cannot_connect_to_google_play")
#elif UNITY_IOS
                content = LocalizationManager.Instance.GetLocalizedString("&key.UI_cannot_connect_to_itunes_store")
#else
                content = LocalizationManager.Instance.GetLocalizedString("&key.UI_purchase_failed")
#endif
            };
            GameGlobal.GetMgr<UIMgr>().OpenSync(UIViewName.UIView_Notice, viewData1);
            return;
        }
        //
        if (GameGlobal.GetMod<AdSys>().IsRemoveAd())
        {
            UIView_Notice.ViewData viewData2 = new UIView_Notice.ViewData()
            {
                showMidBtn = true,
                content = LocalizationManager.Instance.GetLocalizedString("&key.UI_common_notice5"),
                titleKey = LocalizationManager.Instance.GetLocalizedString("&key.UI_common_box_tittle"),
                rightBtnTitle = LocalizationManager.Instance.GetLocalizedString("&key.UI_button_continue")
            };
            GameGlobal.GetMgr<UIMgr>().OpenSync(UIViewName.UIView_Notice, viewData2);
            return;
        }
        //             
        GameGlobal.GetMgr<UIMgr>().ShowWaiting();
        SDK<IAP>.Instance.RestorePurchases(productIds =>
        {
            GameGlobal.GetMgr<UIMgr>().CloseWaiting();
            Debug.Log($"RestorePurchases count {productIds.Count}");
            foreach (var p in productIds) Debug.Log($"RestorePurchases product {p}");

            if (productIds.Count == 0)
            {
                UIView_Notice.ViewData viewData3 = new UIView_Notice.ViewData()
                {
                    showMidBtn = true,
                    content = LocalizationManager.Instance.GetLocalizedString("&key.UI_setting_common_3"),
                    midBtnTitle = LocalizationManager.Instance.GetLocalizedString("&key.UI_button_continue")
                };
                GameGlobal.GetMgr<UIMgr>().OpenSync(UIViewName.UIView_Notice, viewData3);
                return;
            }

            foreach (var productId in productIds)
            {
                var shopCfg = GameGlobal.GetMod<IAPSys>().GetShopCfgByProductId(productId);
                if (shopCfg.GetIAPShopType() == ShopType.NoAd)
                {
                    GameGlobal.GetMod<AdSys>().SetRemoveAd();

                    UIView_Notice.ViewData viewData4 = new UIView_Notice.ViewData()
                    {
                        showMidBtn = true,
                        content = LocalizationManager.Instance.GetLocalizedString("&key.UI_setting_common_2"),
                        midBtnTitle = LocalizationManager.Instance.GetLocalizedString("&key.UI_button_continue")
                    };
                    GameGlobal.GetMgr<UIMgr>().OpenSync(UIViewName.UIView_Notice, viewData4);
                    EventBus.Dispatch(new EventRestorePurchasesSuccess());
                    return;
                }
            }

            UIView_Notice.ViewData viewData5 = new UIView_Notice.ViewData()
            {
                showMidBtn = true,
                content = LocalizationManager.Instance.GetLocalizedString("&key.UI_setting_common_3"),
                midBtnTitle = LocalizationManager.Instance.GetLocalizedString("&key.UI_button_continue")
            };
            GameGlobal.GetMgr<UIMgr>().OpenSync(UIViewName.UIView_Notice, viewData5);
        });
    }

    public void OnApplicationPause(EventOnApplicationPause evt)
    {
        if (!evt.pause)
        {
            RefreshView_NoticeBtn();
        }
    }

    private void OnEventLanguageChange(EventLanguageChange evt)
    {
        Close();
    }
}