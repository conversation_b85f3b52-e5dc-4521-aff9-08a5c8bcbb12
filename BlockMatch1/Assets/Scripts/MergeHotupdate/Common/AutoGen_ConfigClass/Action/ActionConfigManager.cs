/************************************************
 * Action Config Manager class : ActionConfigManager
 * This file is can not be modify !!!
 ************************************************/

using System.Collections.Generic;
using DragonPlus.Core;
using TMGame;


namespace DragonPlus.Config.Action
{
    public class ActionConfigManager : Singleton<ActionConfigManager>
    {
        private Dictionary<int, Table_Action_Rules> rulesDic = new Dictionary<int, Table_Action_Rules>();
        private Dictionary<int, Table_Action_Mapping> mappingDic = new Dictionary<int, Table_Action_Mapping>();
        private Dictionary<int, List<Table_Action_Rules>> rulesDicByGroup = new Dictionary<int, List<Table_Action_Rules>>();
        public void Init()
        {
            var configs = GameGlobal.GetMgr<ConfigMgr>().GetConfigs<Table_Action_Rules>();
            foreach (var config in configs)
            {
                rulesDic.TryAdd(config.ActionGroupId, config);
                if (!rulesDicByGroup.TryGetValue(config.ActionGroupId,out var list))
                {
                    list = new List<Table_Action_Rules>();
                    rulesDicByGroup.TryAdd(config.ActionGroupId, list);
                }
                list.Add(config);
            }
            
            var configs2 = GameGlobal.GetMgr<ConfigMgr>().GetConfigs<Table_Action_Mapping>();
            foreach (var config in configs2)
            {
                mappingDic.TryAdd(config.ActionGroup, config);
            }
        }

        public void Dispose()
        {
            rulesDic?.Clear();
            mappingDic?.Clear();
        }

        public List<Table_Action_Rules> GetRulesList()
        {
            return GameGlobal.GetMgr<ConfigMgr>().GetConfigs<Table_Action_Rules>();
        }
        
        
        public Dictionary<int,List<Table_Action_Rules>> GetRulesDic()
        {
            return rulesDicByGroup;
        }

        public Table_Action_Mapping GetMaping(int id)
        {
            Table_Action_Mapping mapping = null;
            mappingDic.TryGetValue(id, out  mapping);
             return mapping;
        }
    }
}