/************************************************
 * Config class : Table_InGame_ChapterReward
 * This file is can not be modify !!!
 ************************************************/

using System;
using System.Collections.Generic;
using Config;

namespace DragonPlus.Config.InGame
{
    public partial class Table_InGame_ChapterReward:ConfigBase
    {   
        /// <summary>
        /// #ID
        /// </summary>
        public int Id { get; set; }
        
        /// <summary>
        /// #宝箱奖励类型; 1、金币; 8、装修币; 101、道具-旋转; 102、道具-炸弹; 103、道具-单块
        /// </summary>
        public List<int> RewardType { get; set; }
        
        /// <summary>
        /// #宝箱奖励数量
        /// </summary>
        public List<int> RewardNumber { get; set; }
        
        /// <summary>
        /// #解锁的装修挂点ID，没有则为空
        /// </summary>
        public int UnlockRoomNodeId { get; set; }
        
        /// <summary>
        /// #解锁的装修挂点ICON，没有则为空
        /// </summary>
        public string RoomNodeIcon { get; set; }
        

        public override int GetId()
        {
            return Id;
        }
    }
}