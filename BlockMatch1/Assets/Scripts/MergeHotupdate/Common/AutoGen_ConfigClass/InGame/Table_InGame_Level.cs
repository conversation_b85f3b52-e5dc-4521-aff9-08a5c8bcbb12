/************************************************
 * Config class : Table_InGame_Level
 * This file is can not be modify !!!
 ************************************************/

using System;
using System.Collections.Generic;
using Config;

namespace DragonPlus.Config.InGame
{
    public partial class Table_InGame_Level:ConfigBase
    {   
        /// <summary>
        /// #ID
        /// </summary>
        public int Id { get; set; }
        
        /// <summary>
        /// #ID-真实关卡顺序
        /// </summary>
        public int Sort { get; set; }
        
        /// <summary>
        /// #测试组
        /// </summary>
        public int AbTestGroup { get; set; }
        
        /// <summary>
        /// #JSON文件名
        /// </summary>
        public string JsonFileName { get; set; }
        
        /// <summary>
        /// 引用的块待选池组ID
        /// </summary>
        public int BlockGroupId { get; set; }
        
        /// <summary>
        /// #剧本组
        /// </summary>
        public int StoryGroup { get; set; }
        
        /// <summary>
        /// 首轮出块的ID，不配置则按照算法出块
        /// </summary>
        public List<int> FirstBlockId { get; set; }
        
        /// <summary>
        /// 关卡目标数达成的比例-百分比; （分数或宝石数）
        /// </summary>
        public List<int> LevelTargetRatio { get; set; }
        
        /// <summary>
        /// 1、构筑算法; 2、消除算法; 3、随机算法
        /// </summary>
        public List<int> EasyExperienceType { get; set; }
        
        /// <summary>
        /// 对应算法的概率; （百分比）
        /// </summary>
        public List<int> EasyExperienceWeight { get; set; }
        
        /// <summary>
        /// 1：构筑-减少边数; 2：构筑-复杂度增加
        /// </summary>
        public List<int> ComplexityUpWeight1 { get; set; }
        
        /// <summary>
        /// 1：消除-减少边数; 2：消除-优先消除
        /// </summary>
        public List<int> ComplexityDownWeight1 { get; set; }
        
        /// <summary>
        /// 1、构筑算法; 2、消除算法; 3、随机算法
        /// </summary>
        public List<int> NormalExperienceType { get; set; }
        
        /// <summary>
        /// 对应算法的概率; （百分比）
        /// </summary>
        public List<int> NormalExperienceWeight { get; set; }
        
        /// <summary>
        /// 1：构筑-减少边数; 2：构筑-复杂度增加
        /// </summary>
        public List<int> ComplexityUpWeight2 { get; set; }
        
        /// <summary>
        /// 1：消除-减少边数; 2：消除-优先消除
        /// </summary>
        public List<int> ComplexityDownWeight2 { get; set; }
        
        /// <summary>
        /// 1、构筑算法; 2、消除算法; 3、随机算法
        /// </summary>
        public List<int> HardExperienceType { get; set; }
        
        /// <summary>
        /// 对应算法的概率; （百分比）
        /// </summary>
        public List<int> HardExperienceWeight { get; set; }
        
        /// <summary>
        /// 1：构筑-减少边数; 2：构筑-复杂度增加
        /// </summary>
        public List<int> ComplexityUpWeight3 { get; set; }
        
        /// <summary>
        /// 1：消除-减少边数; 2：消除-优先消除
        /// </summary>
        public List<int> ComplexityDownWeight3 { get; set; }
        
        /// <summary>
        /// 难题触发时，关卡完成进度的百分比（概率取整）; ≥ 该值时则优先判断难题算法
        /// </summary>
        public int LevelProgressDiffTrigger { get; set; }
        
        /// <summary>
        /// 难题触发的最大次数; （针对HARD模式，0-3）
        /// </summary>
        public int ChallengeLimitedTime { get; set; }
        
        /// <summary>
        /// 块生成宝石的概率; （百分比）
        /// </summary>
        public int GemAppearWeight { get; set; }
        
        /// <summary>
        /// 生成宝石块的数量范围; (小于等于上限值)
        /// </summary>
        public List<int> GemNumberRange { get; set; }
        
        /// <summary>
        /// 宝石种类生成的权重（和GEMSTONETYPE字段对应）
        /// </summary>
        public List<int> GemstoneTypeWeight { get; set; }
        

        public override int GetId()
        {
            return Id;
        }
    }
}