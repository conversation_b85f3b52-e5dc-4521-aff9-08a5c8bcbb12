/************************************************
 * Config class : Table_InGame_ClearScreen
 * This file is can not be modify !!!
 ************************************************/

using System;
using System.Collections.Generic;
using Config;

namespace DragonPlus.Config.InGame
{
    public partial class Table_InGame_ClearScreen:ConfigBase
    {   
        /// <summary>
        /// #档位
        /// </summary>
        public int Id { get; set; }
        
        /// <summary>
        /// 关卡布局ID
        /// </summary>
        public string LayoutBinary { get; set; }
        
        /// <summary>
        /// 出块的ID
        /// </summary>
        public List<int> GenerateBlockId { get; set; }
        

        public override int GetId()
        {
            return Id;
        }
    }
}