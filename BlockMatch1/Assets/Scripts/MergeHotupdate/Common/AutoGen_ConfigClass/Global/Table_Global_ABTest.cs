/************************************************
 * Config class : Table_Global_ABTest
 * This file is can not be modify !!!
 ************************************************/

using System;
using System.Collections.Generic;
using Config;

namespace DragonPlus.Config.Global
{
    public partial class Table_Global_ABTest:ConfigBase
    {   
        /// <summary>
        /// #ID
        /// </summary>
        public int Id { get; set; }
        
        /// <summary>
        /// #ABTEST类型
        /// </summary>
        public int AbTestType { get; set; }
        
        /// <summary>
        /// #ABTEST的KEY
        /// </summary>
        public string AbTestKey { get; set; }
        
        /// <summary>
        /// #是否使用客户端固化
        /// </summary>
        public bool UseClient { get; set; }
        
        /// <summary>
        /// #参与固化的用户最小首次登录APP版本号
        /// </summary>
        public string ClientMinAppVersion { get; set; }
        

        public override int GetId()
        {
            return Id;
        }
    }
}