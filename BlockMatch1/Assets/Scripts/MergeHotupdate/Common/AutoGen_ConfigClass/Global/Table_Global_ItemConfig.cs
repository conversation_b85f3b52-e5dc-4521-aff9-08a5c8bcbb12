/************************************************
 * Config class : Table_Global_ItemConfig
 * This file is can not be modify !!!
 ************************************************/

using System;
using System.Collections.Generic;
using Config;

namespace DragonPlus.Config.Global
{
    public partial class Table_Global_ItemConfig:ConfigBase
    {   
        /// <summary>
        /// #ID
        /// </summary>
        public int Id { get; set; }
        
        /// <summary>
        /// #物品类型
        /// </summary>
        public int ItemType { get; set; }
        
        /// <summary>
        /// #名称
        /// </summary>
        public string Name { get; set; }
        
        /// <summary>
        /// 解锁关卡; （针对关卡模式）
        /// </summary>
        public int UnlockLevel { get; set; }
        
        /// <summary>
        /// 初始数量
        /// </summary>
        public int InitAmount { get; set; }
        
        /// <summary>
        /// #价格（单位COIN）
        /// </summary>
        public int Price { get; set; }
        

        public override int GetId()
        {
            return Id;
        }
    }
}